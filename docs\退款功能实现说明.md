# 退款功能实现说明

## 功能概述

为历史订单页面添加了完整的退款功能，支持全额退款和部分退款，包含退款原因记录、权限控制、余额处理等完整的退款流程。

## 主要功能特点

### 1. 退款方式支持
- **余额退回**: 退款金额直接退回到客户余额账户
- **现金退款**: 现金方式退款（不影响系统余额）
- **原路退回**: 按原支付方式退回（不影响系统余额）

### 2. 退款类型
- **全额退款**: 退款金额等于订单总金额
- **部分退款**: 退款金额小于订单总金额

### 3. 退款后状态处理
**订单状态 (order.status)**:
- 全额退款：自动更新为 `'已退赔'`
- 部分退款：保持原状态不变

**支付状态 (order.payment_status)**:
- 全额退款：自动更新为 `'已退款'`
- 部分退款：自动更新为 `'部分退款'`

### 3. 权限控制
- 只有管理员或订单的原操作员可以进行退款操作
- 已退赔状态的订单不能重复退款

### 4. 数据完整性
- 完整的退款记录保存
- 订单状态自动更新
- 客户余额自动处理（余额退回时）

## 技术实现

### 1. 数据库层面

#### 新增退款记录表 (refund_record)
```sql
CREATE TABLE refund_record (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    order_number VARCHAR(50) NOT NULL,
    customer_id INT NOT NULL,
    refund_amount DECIMAL(10,2) NOT NULL,
    original_amount DECIMAL(10,2) NOT NULL,
    original_payment_method VARCHAR(50) NOT NULL,
    refund_method VARCHAR(50) NOT NULL,
    refund_reason TEXT,
    refund_type VARCHAR(20) NOT NULL DEFAULT '全额退款',
    status VARCHAR(20) NOT NULL DEFAULT '已退款',
    operator VARCHAR(50) NOT NULL,
    approved_by VARCHAR(50),
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME
);
```

### 2. 后端实现

#### 新增模型 (models.py)
- `RefundRecord`: 退款记录模型，包含完整的退款信息字段

#### 新增工具函数 (utils.py)
- `process_refund()`: 核心退款处理函数，处理不同退款方式的业务逻辑

#### 新增API路由 (app.py)
- `POST /api/orders/<id>/refund`: 处理订单退款
- `GET /api/orders/<id>/refund_records`: 获取订单退款记录
- `GET /api/refund_records`: 获取退款记录列表（支持分页和筛选）

### 3. 前端实现

#### 用户界面
- 在订单详情模态框中添加退款按钮
- 退款模态框包含订单信息展示和退款表单
- 支持全额退款快捷按钮

#### JavaScript功能
- `openRefundModal()`: 打开退款模态框
- `processRefund()`: 处理退款请求
- 表单验证和用户交互

#### CSS样式
- 退款按钮样式（红色主题）
- 退款模态框样式
- 表单元素样式

## 使用流程

### 1. 发起退款
1. 在历史订单页面查找需要退款的订单
2. 点击"详情"按钮打开订单详情
3. 点击"退款"按钮（仅未退赔订单显示）
4. 填写退款信息：
   - 退款金额（可点击"全额退款"快速填入）
   - 退款方式（余额退回/现金退款/原路退回）
   - 退款原因（必填）

### 2. 确认退款
1. 系统显示退款确认信息
2. 确认后系统处理退款：
   - 创建退款记录
   - 更新订单状态（全额退款时）
   - 处理客户余额（余额退回时）

### 3. 退款完成
1. 显示退款成功信息
2. 自动刷新订单列表
3. 订单状态更新为"已退赔"（全额退款时）

## 权限说明

### 退款权限
- **管理员**: 可以对所有订单进行退款操作
- **普通员工**: 只能对自己创建的订单进行退款操作

### 查看权限
- **管理员**: 可以查看所有退款记录
- **普通员工**: 只能查看自己操作的退款记录

## 业务规则

### 1. 退款金额验证
- 退款金额必须大于0
- 退款金额不能超过订单总金额
- 支持部分退款（金额小于订单总金额）

### 2. 订单状态处理
- 全额退款：订单状态更新为"已退赔"
- 部分退款：订单状态保持不变
- 已退赔订单不能再次退款

### 3. 余额处理
- 余额退回：直接增加客户充值余额
- 其他退款方式：不影响客户余额

## 安全考虑

### 1. 权限验证
- 每次退款操作都进行权限检查
- 防止越权操作

### 2. 数据验证
- 严格的输入验证
- 防止SQL注入和XSS攻击

### 3. 操作记录
- 完整的退款操作日志
- 可追溯的操作历史

## 部署说明

### 1. 数据库更新
执行数据库迁移脚本：
```bash
mysql -u username -p database_name < database_migrations/add_refund_table.sql
```

### 2. 代码部署
1. 更新 models.py（新增RefundRecord模型）
2. 更新 utils.py（新增process_refund函数）
3. 更新 app.py（新增退款API路由）
4. 更新 templates/history.html（新增退款界面）

### 3. 重启服务
重启应用服务器使更改生效

## 测试建议

### 1. 功能测试
- 测试全额退款流程
- 测试部分退款流程
- 测试不同退款方式
- 测试权限控制

### 2. 边界测试
- 测试退款金额边界值
- 测试已退赔订单的处理
- 测试权限边界

### 3. 集成测试
- 测试与订单系统的集成
- 测试与客户余额系统的集成
- 测试数据一致性

## 维护说明

### 1. 监控要点
- 退款操作频率
- 退款金额统计
- 异常退款记录

### 2. 数据备份
- 定期备份退款记录
- 确保数据安全

### 3. 性能优化
- 监控退款查询性能
- 适时添加索引优化
