import os
import uuid
import base64
import datetime
import io
from flask import current_app
from sqlalchemy import func
from models import db, Order

# 增加生成条形码所需的依赖
try:
    from PIL import Image, ImageDraw, ImageFont
    import barcode  # 添加python-barcode库依赖
    from barcode.writer import ImageWriter
except ImportError:
    print("警告: PIL库或barcode库未安装，条形码生成功能将不可用")
    print("请使用pip安装: pip install pillow python-barcode")

def save_base64_image(base64_data, customer_phone, index=0):
    """将Base64格式的图片数据保存为文件

    Args:
        base64_data: Base64编码的图片数据
        customer_phone: 客户手机号
        index: 图片索引

    Returns:
        str: 相对路径，失败返回None
    """
    try:
        print(f"开始保存图片: 客户 {customer_phone}, 索引 {index}")
        if not base64_data:
            print("空的base64数据")
            return None

        if isinstance(base64_data, str) and len(base64_data) < 100:
            print(f"base64数据异常短: {base64_data[:20]}...")
            return None

        timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        random_id = uuid.uuid4().hex[:8]
        filename = f"{customer_phone}_{timestamp}_{random_id}_{index}.jpg"

        # 确保使用相对路径
        upload_folder = current_app.config['UPLOAD_FOLDER']
        filepath = os.path.join(upload_folder, filename)

        # 确保目标目录存在
        full_dir_path = os.path.join(current_app.root_path, 'static', upload_folder)
        print(f"创建目录: {full_dir_path}")
        os.makedirs(full_dir_path, exist_ok=True)

        full_file_path = os.path.join(current_app.root_path, 'static', filepath)
        print(f"保存图片到: {full_file_path}")

        # 将Base64数据解码为二进制
        if isinstance(base64_data, str):
            # 检查是否包含data:image前缀
            if ',' in base64_data:
                print("从数据URL中提取Base64内容")
                base64_data = base64_data.split(',')[1]

            # 解码Base64
            try:
                binary_data = base64.b64decode(base64_data)
                print(f"解码成功，数据大小: {len(binary_data)} 字节")
            except Exception as e:
                print(f"Base64解码失败: {str(e)}")
                return None
        else:
            print(f"不是字符串类型的Base64数据: {type(base64_data)}")
            return None

        # 写入文件
        with open(full_file_path, "wb") as fh:
            fh.write(binary_data)
            print(f"图片文件写入成功")

        # 返回相对路径
        return filepath
    except Exception as e:
        print(f"保存图片失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def generate_order_number():
    """生成订单编号 - 6位纯数字的顺序编号

    Returns:
        str: 6位纯数字订单编号
    """
    try:
        # 获取当前最大订单号，如果不存在则从100000开始
        max_order = db.session.query(func.max(Order.order_number)).scalar()

        # 检查最大订单号是否为6位数字
        if max_order is None or not max_order.isdigit() or len(max_order) != 6:
            # 如果没有订单或订单号格式不是6位纯数字，则从初始值开始
            new_order_num = 100000  # 6位数字起始值
        else:
            # 已有6位订单号，递增
            new_order_num = int(max_order) + 1

        # 如果递增后超过6位，则重新从100000开始
        if new_order_num >= 1000000:
            new_order_num = 100000

        # 在生成订单号后，检查是否已存在
        while Order.query.filter_by(order_number=str(new_order_num)).first() is not None:
            # 如果订单号已存在，则继续递增
            new_order_num += 1
            # 如果递增后超过6位，则从100000重新开始
            if new_order_num >= 1000000:
                new_order_num = 100000

        # 转为字符串返回
        return str(new_order_num)
    except Exception as e:
        print(f"生成订单号出错: {str(e)}")
        # 出错时使用时间戳+随机数作为备选方案
        timestamp = datetime.datetime.now().strftime('%y%m%d%H%M')
        random_id = str(uuid.uuid4().int)[:3]
        fallback_number = f"{timestamp}{random_id}"[-6:].zfill(6)

        # 同样检查备用订单号是否重复
        while Order.query.filter_by(order_number=fallback_number).first() is not None:
            random_id = str(uuid.uuid4().int)[:3]
            fallback_number = f"{timestamp}{random_id}"[-6:].zfill(6)

        return fallback_number

def update_customer_balance(customer, amount, is_recharge=True, gift_amount=0.0):
    """更新客户余额

    Args:
        customer: 客户对象
        amount: 金额
        is_recharge: 是否为充值，默认为True。
                    如果为False，则为消费，金额为负数
        gift_amount: 赠送金额（仅在充值时使用）

    Returns:
        float: 更新后的总余额
    """
    if is_recharge:
        # 充值：增加充值余额和赠送余额
        customer.balance += amount
        if gift_amount > 0:
            customer.gift_balance = (customer.gift_balance or 0.0) + gift_amount
    else:
        # 消费：优先使用赠送余额，不足时使用充值余额
        remaining_amount = amount

        # 先扣除赠送余额
        if customer.gift_balance and customer.gift_balance > 0:
            if customer.gift_balance >= remaining_amount:
                customer.gift_balance -= remaining_amount
                remaining_amount = 0
            else:
                remaining_amount -= customer.gift_balance
                customer.gift_balance = 0

        # 如果还有剩余金额，从充值余额中扣除
        if remaining_amount > 0:
            customer.balance -= remaining_amount

    return customer.balance + (customer.gift_balance or 0.0)

def calculate_gift_amount(recharge_amount):
    """计算充值赠送金额

    Args:
        recharge_amount: 充值金额

    Returns:
        float: 赠送金额
    """
    from models import RechargeGiftRule

    try:
        # 查询所有启用的赠送规则，按最小金额降序排列
        rules = RechargeGiftRule.query.filter(
            RechargeGiftRule.is_active == True,
            RechargeGiftRule.min_amount <= recharge_amount
        ).order_by(RechargeGiftRule.min_amount.desc()).all()

        if not rules:
            return 0.0

        # 使用第一个匹配的规则（金额最高的规则）
        rule = rules[0]

        if rule.gift_type == 'percentage':
            # 按比例赠送
            return recharge_amount * (rule.gift_value / 100.0)
        elif rule.gift_type == 'fixed':
            # 固定金额赠送
            return rule.gift_value
        else:
            return 0.0

    except Exception as e:
        print(f"计算赠送金额失败: {str(e)}")
        return 0.0

def generate_barcode_base64(order_number, item_index=0, operator_name="", phone="", remarks="", defects=""):
    """根据订单号生成条形码的base64编码图片，优化布局以适应120mm*20mm的打印纸

    Args:
        order_number: 订单号
        item_index: 衣物索引
        operator_name: 营业员姓名
        phone: 客户手机号
        remarks: 备注内容
        defects: 瑕疵内容

    Returns:
        str: base64编码的条形码图片
    """
    try:
        # 确保PIL已安装
        if 'Image' not in globals() or 'barcode' not in globals():
            print("PIL库或barcode库未安装，无法生成条形码")
            return None

        # 确保订单号是纯数字并有6位
        order_number = str(order_number).replace('-', '').replace(' ', '')
        if not order_number.isdigit():
            order_number = ''.join(c for c in order_number if c.isdigit())
        if len(order_number) != 6:
            order_number = order_number.zfill(6)

        # 创建适合120mm*20mm尺寸的图像(6:1比例)
        width, height = 1200, 200  # 调整为符合打印纸比例的尺寸
        # 白色背景
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)

        # 使用更小的字体尺寸以适应窄高度
        try:
            small_font = ImageFont.truetype("arial.ttf", 14)
            medium_font = ImageFont.truetype("arial.ttf", 16)
            large_font = ImageFont.truetype("arial.ttf", 20)
        except Exception as e:
            # 如果找不到指定字体，尝试使用系统默认字体
            try:
                from PIL import ImageFont
                small_font = ImageFont.load_default()
                medium_font = ImageFont.load_default()
                large_font = ImageFont.load_default()
            except:
                small_font = None
                medium_font = None
                large_font = None
                print(f"加载字体错误: {str(e)}")

        # 不再绘制任何左侧信息，全部留给条形码使用

        # 使用python-barcode库生成条形码
        # 构建用于条形码的数据：使用完整订单号
        barcode_data = order_number + "-" + str(item_index+1).zfill(2)

        # 创建一个内存中的文件对象用于保存条形码图像
        barcode_buffer = io.BytesIO()

        # 使用Code128格式创建条形码
        code128 = barcode.get_barcode_class('code128')

        # 配置条形码写入器 - 调整条码高度以减少留白
        barcode_options = {
            'module_height': 80.0,  # 降低条码高度以适应15mm高度
            'module_width': 1.2,     # 调整条码宽度
            'quiet_zone': 1.0,       # 保持最小的两侧空白区
            'font_size': 0,          # 不显示条码下方文字
            'text_distance': 0.5,    # 最小化文字与条码的距离
        }
        barcode_writer = ImageWriter()
        for option, value in barcode_options.items():
            setattr(barcode_writer, option, value)

        # 生成条形码并保存到内存缓冲区
        barcode_image = code128(barcode_data, writer=barcode_writer)
        barcode_image.write(barcode_buffer)

        # 读取生成的条形码图像
        barcode_buffer.seek(0)
        barcode_img = Image.open(barcode_buffer)

        # 调整条形码图像大小以匹配整体布局
        barcode_width = 750  # 调整宽度为750像素 (75mm,基于120mm=1200像素)
        barcode_height = 150 # 调整高度为150像素 (15mm,基于20mm=200像素)
        barcode_img = barcode_img.resize((barcode_width, barcode_height), Image.LANCZOS)

        # 计算条形码放置位置（水平居中且垂直居中）
        barcode_x = (width - barcode_width) // 2  # 水平居中
        barcode_y = (height - barcode_height) // 2  # 垂直居中

        # 将条形码图像粘贴到主图像
        image.paste(barcode_img, (barcode_x, barcode_y))

        # 不添加额外文本，让HTML内的元素处理文本显示

        # 转换为base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode('utf-8')

        return f"data:image/png;base64,{img_str}"
    except Exception as e:
        print(f"生成条形码出错: {str(e)}")
        import traceback
        traceback.print_exc()
        # 出错时返回默认编码
        return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEzUlEQVR42u3dW2icRRjG8X9m1kRTA6YxSROVSiHSWlsrVpqLSm+sF1YIgVjw7EURRfBGBL2wiVgQFDz0QvBYRetZWxWpJ6SJxhJTLCHGJk3TJDY12WY/L7KJTZrdnf3m+6Zn5n3uFtJ8+5/nTHa/WUUIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCrBzKeAQFvArcAZQ5jOVfYAg4APwEeAZzF9AP3AtkgLXAReA4sAeYNJy9Cqw3nAnwO3Cs0HcnvQhewPxDWSlSQI5F1sBqcZhppHXQyxjMDNx0I7qS9MHGfqrAMEPAO8DXwHGgv9BXAluA24HngZuAswazU8AMkIXMSX3xY6/X6fI6D+YrjwCvAe8DHwLTBlNqCgXbAujPTrAKaASuL0QXMGooux5IFY6HtPZubWrzDrXfU9MBTndGQfSgn0zP7h8+c/HdSZt51VQANgvFQBroLfw0/QYcARoK10CuAd4ETlrKviawPRO8nF+8OQ10bZoFptLdnYMblH8gckXxRVc7AHVHXL86zHRVAdSwDxpXugEYSX8MnO0cjsZPdxR+P3p2Uy3+sUzvcAl+d7orSneNZnoHK6K9LcWXKh2AsN4yuwZoRGc758AbbQTv0dTL+W/5UUBnOpuj8fwJYH9L20LW6a7zcbSrpfhipQMQahbOAbQGb6zRBH+0CbyDjX4Ubz9WfH1fSywvz5nOUYNxcYYCaCzx2m9wUHy0g+DDJn86f9K/IXYSc9Lg3BxgMeXNAVZ07Q81ms6fzn8o1+RH8e/bYnl55r4uF3NzgEWl5gAVnwR+FHjRxmD92QfBx4326/ydTbH8wuP5iWDxZrCjeTYHqNgkcHnFx60+5wJR/GEz+F3Ns1F8W+y6vDzfnQtFwDU5wCWrGkB7fV4PRPGvLUT7Wv0o3tm8sJcf9GHvhiNzgIIYBmB9EpjuXvAc4Hfn5wA7mmJr51/j3dS7qZdL1uD/NKCRw8aLP9Dszztu7/tR/PHWhdnn/vCvON21qMw0Th+DXfPmAP9VBWB9Euh35yeBJ5r8rX1P84Isvzt/yRQ3s4ZLLCeP6dnB0twg/LLnAKoYAM9fBYCdSeDZhXOA0+1+d376k8UzYe4Pf3J2AOSTPsdN4pfDRuA04eXvUKoeQLhpwacV79sZnL+VH8U7Wjad4eNZv7w8r52dZJ9qHVQelKMbh4oHEP5iQVJg0CT/XFs/it92cmEvzymgO3eSVEcj/vXAy0CX4VijpgAiGIClOcC5JvCns2v60/n7Hqrxo/gTJ/JS/BRQHSs+1dqtQz+G39lkMrbYFUEACfA2C++XHt7P1H/+lj+df+TbxbP+k8CH+NP54VJrfxT3PhBnP/LBL6G8WBLyHoDT+wLaW3Z442n4/+38N/Kfx/GXlRYOgOH9AOQnlfPWzzr5Sw/n7yw6+auAV3CfX5PUFcDlW8SSnAP4e/WTkJ/E2aM88e0AJUcA60D72QfB7o7ZrXp4/1ZuDj9zPM4etdT5C4iiX73GmRmAjTt3Xb0fcDHgCDjNLmO2BACyACSe1LkLLwFIAAkf6tSFt+oD+A9urzIWW7mCBwAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAyMC0wNi0wMlQxODoxNjozOSswMDowMGVRhRQAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMjAtMDYtMDJUMTg6MTY6MzkrMDA6MDAUDDyoAAAAAElFTkSuQmCC"

def process_refund(order, refund_amount, refund_reason, operator, refund_method='余额退回'):
    """处理订单退款

    Args:
        order: 订单对象
        refund_amount: 退款金额
        refund_reason: 退款原因
        operator: 操作员
        refund_method: 退款方式，默认为余额退回

    Returns:
        dict: 退款处理结果
    """
    from models import db, RefundRecord, Customer

    try:
        # 验证退款金额
        if refund_amount <= 0:
            return {'success': False, 'error': '退款金额必须大于0'}

        if refund_amount > order.total_amount:
            return {'success': False, 'error': '退款金额不能超过订单金额'}

        # 检查订单状态
        if order.status in ['已退赔']:
            return {'success': False, 'error': '订单已经退款，不能重复退款'}

        # 获取客户信息
        customer = Customer.query.get(order.customer_id)
        if not customer:
            return {'success': False, 'error': '客户信息不存在'}

        # 确定退款类型
        refund_type = '全额退款' if refund_amount == order.total_amount else '部分退款'

        # 处理不同支付方式的退款
        if order.payment_method == '余额' or refund_method == '余额退回':
            # 余额支付或选择余额退回，直接退回到客户余额
            customer.balance = (customer.balance or 0.0) + refund_amount
            actual_refund_method = '余额退回'
        else:
            # 其他支付方式，根据选择的退款方式处理
            if refund_method == '余额退回':
                customer.balance = (customer.balance or 0.0) + refund_amount
                actual_refund_method = '余额退回'
            else:
                # 现金退款或原路退回，不修改客户余额
                actual_refund_method = refund_method

        # 创建退款记录
        refund_record = RefundRecord(
            order_id=order.id,
            order_number=order.order_number,
            customer_id=order.customer_id,
            refund_amount=refund_amount,
            original_amount=order.total_amount,
            original_payment_method=order.payment_method,
            refund_method=actual_refund_method,
            refund_reason=refund_reason,
            refund_type=refund_type,
            operator=operator,
            processed_at=datetime.datetime.now()
        )

        # 更新订单状态和支付状态
        if refund_type == '全额退款':
            order.status = '已退赔'
            order.payment_status = '已退款'  # 全额退款时支付状态改为已退款
        else:
            # 部分退款时，支付状态改为部分退款
            order.payment_status = '部分退款'

        # 保存到数据库
        db.session.add(refund_record)
        db.session.commit()

        return {
            'success': True,
            'message': f'{refund_type}处理成功',
            'refund_record': {
                'id': refund_record.id,
                'refund_amount': refund_amount,
                'refund_method': actual_refund_method,
                'refund_type': refund_type,
                'new_balance': customer.total_balance if actual_refund_method == '余额退回' else None
            }
        }

    except Exception as e:
        db.session.rollback()
        return {'success': False, 'error': f'退款处理失败: {str(e)}'}